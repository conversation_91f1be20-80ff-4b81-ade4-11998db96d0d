import { MidwayConfig } from '@midwayjs/core';

export default {
  // Sequelize 数据库配置
  sequelize: {
    dataSource: {
      default: {
        database: 'zhi_hui_y<PERSON>_jian',
        username: 'root',
        password: 'Aa@123456',
        host: '***********',
        port: 3306,
        dialect: 'mysql',
        // 数据库连接选项
        dialectOptions: {
          connectTimeout: 60000,
          supportBigNumbers: true,
          bigNumberStrings: true,
          debug: false,
        },
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          freezeTableName: true,
          // 禁用驼峰转换
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_unicode_ci',
        },
        // 时区设置
        timezone: '+08:00',
        // 连接池配置
        pool: {
          max: 5,
          min: 0,
          idle: 10000,
        },
        entities: ['entity'],
        logging: false,
        // 修改同步配置
        sync: true, // 重新启用自动同步
        // syncOptions: {
        //   alter: true,
        //   logging: true, // 关闭同步时的详细日志
        // },
      },
    },
  },
  // Redis 配置
  redis: {
    clients: {
      default: {
        port: 6379,
        host: '***********',
        password: 'Aa@123456',
        db: 0,
      },
      dictionary: {
        port: 6379,
        host: '***********',
        password: 'Aa@123456',
        db: 1,
      },
    },
  },
} as MidwayConfig;
