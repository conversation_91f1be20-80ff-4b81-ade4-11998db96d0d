import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 用户登录DTO
 */
export class LoginDTO {
  @Rule(RuleType.string().required().min(3).max(50))
  username: string;

  @Rule(RuleType.string().required().min(6).max(50))
  password: string;
}

/**
 * 用户创建DTO
 */
export class CreateUserDTO {
  @Rule(RuleType.string().required().min(3).max(50))
  username: string;

  @Rule(RuleType.string().required().min(6).max(50))
  password: string;

  @Rule(RuleType.string().valid('admin', 'user').default('user'))
  role?: string = 'user';

  @Rule(RuleType.boolean().default(true))
  enabled?: boolean = true;
}

/**
 * 用户更新DTO
 */
export class UpdateUserDTO {
  @Rule(RuleType.string().optional().min(3).max(50))
  username?: string;

  @Rule(RuleType.string().optional().min(6).max(50))
  password?: string;

  @Rule(RuleType.string().valid('admin', 'user').optional())
  role?: string;

  @Rule(RuleType.boolean().optional())
  enabled?: boolean;
}

/**
 * 用户响应DTO
 */
export class UserResponseDTO {
  id: number;
  username: string;
  role: string;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;

  constructor(user: any) {
    this.id = user.id;
    this.username = user.username;
    this.role = user.role;
    this.enabled = user.enabled;
    this.createdAt = user.createdAt;
    this.updatedAt = user.updatedAt;
  }
}

/**
 * 登录响应DTO
 */
export class LoginResponseDTO {
  token: string;
  user: UserResponseDTO;

  constructor(token: string, user: any) {
    this.token = token;
    this.user = new UserResponseDTO(user);
  }
}
