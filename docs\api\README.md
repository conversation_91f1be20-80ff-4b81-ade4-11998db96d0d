# 智慧营建系统 API 文档

## 概述

智慧营建系统提供完整的 RESTful API 接口，用于管理和展示关中地区的山塬、水系、历史要素等地理文化数据。

## 基础信息

- **基础URL**: `http://localhost:7001`
- **API版本**: v1.0
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证方式

系统采用 JWT (JSON Web Token) 认证方式：

1. 通过登录接口获取 token
2. 在请求头中添加：`Authorization: Bearer <token>`
3. 管理端接口需要认证，公共接口无需认证

## 统一响应格式

**注意**: 新架构已移除统一的ResponseDTO包装，直接返回业务数据。

### 成功响应
```json
// 直接返回业务数据
{
  "id": 1,
  "name": "华山",
  "longitude": 110.0910,
  "latitude": 34.4880
}

// 或分页数据
{
  "data": [...],
  "total": 100,
  "page": 1,
  "pageSize": 10
}
```

### 错误响应
```json
{
  "message": "错误信息",
  "statusCode": 400,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/xxx"
}
```

## 分页响应格式

```json
{
  "data": [],
  "total": 100,
  "page": 1,
  "pageSize": 10
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如重复创建） |
| 500 | 服务器内部错误 |

## API 模块

### 🌐 公共接口 (无需认证)
- [地图数据接口](./public/map.md) - 地图展示数据
- [数据查询接口](./public/data.md) - 实体数据查询
- [字典数据接口](./public/dictionary.md) - 字典数据获取
- [统计分析接口](./public/statistic.md) - 统计分析数据

### 🔐 管理端接口 (需要认证)
- [认证管理接口](./admin/auth.md) - 用户登录认证
- [山塬管理接口](./admin/mountain.md) - 山塬数据管理
- [水系管理接口](./admin/water-system.md) - 水系数据管理
- [历史要素管理接口](./admin/historical-element.md) - 历史要素管理
- [字典管理接口](./admin/dictionary.md) - 字典数据管理
- [系统管理接口](./admin/system.md) - 系统管理功能

### 📁 通用接口
- [文件上传接口](./upload.md) - 文件上传功能

## 快速开始

### 1. 获取地图数据
```bash
curl -X GET "http://localhost:7001/public/map/data"
```

### 2. 用户登录
```bash
curl -X POST "http://localhost:7001/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 3. 创建山塬数据
```bash
curl -X POST "http://localhost:7001/admin/mountain" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "华山",
    "code": "HS001",
    "longitude": 110.0910,
    "latitude": 34.4880,
    "height": 2154,
    "regionDictId": 1
  }'
```

## 注意事项

1. **时间格式**: 统一使用 ISO 8601 格式 (`YYYY-MM-DDTHH:mm:ss.sssZ`)
2. **坐标系统**: 使用 WGS84 坐标系统
3. **文件上传**: 支持的图片格式为 jpg, jpeg, png, gif, bmp, webp
4. **请求限制**: 单次上传文件大小限制为 50MB
5. **分页参数**: page 从 1 开始，pageSize 最大为 100

## 错误处理

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| 用户名或密码错误 | 登录凭据无效 | 检查用户名和密码 |
| 认证令牌无效或已过期 | Token 失效 | 重新登录获取新 token |
| 权限不足 | 非管理员用户访问管理接口 | 使用管理员账户登录 |
| 数据不存在 | 请求的资源不存在 | 检查资源 ID 是否正确 |
| 参数验证失败 | 请求参数不符合要求 | 检查参数格式和必填项 |
