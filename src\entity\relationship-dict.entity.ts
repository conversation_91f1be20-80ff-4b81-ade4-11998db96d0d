import {
  Table,
  Column,
  DataType,
  HasMany,
  BelongsTo,
  ForeignKey,
  Model,
} from 'sequelize-typescript';

export interface RelationshipDictAttributes {
  /** ID */
  id: number;
  /** 关系编码 */
  relationCode?: string;
  /** 关系名称 */
  relationName?: string;
  /** 父级关系ID */
  parentId?: number;
  /** 状态 */
  status?: number;
  /** 排序号 */
  sort?: number;
  /** 关系描述 */
  relationDesc?: string;
}

/**
 * 关系字典表模型
 */
@Table({
  tableName: 'relationship_dict',
  comment: '关系字典表',
})
export class RelationshipDict
  extends Model<RelationshipDictAttributes>
  implements RelationshipDictAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: true,
    comment: '关系编码（唯一）',
    field: 'relation_code',
  })
  relationCode: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '关系名称',
    field: 'relation_name',
  })
  relationName: string;

  @ForeignKey(() => RelationshipDict)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
    comment: '父级关系ID',
    field: 'parent_id',
  })
  parentId: number;

  @Column({
    type: DataType.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '状态（1启用，0禁用）',
  })
  status: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '排序号',
  })
  sort: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '关系说明',
    field: 'relation_desc',
  })
  relationDesc: string;

  // 自关联关系
  @BelongsTo(() => RelationshipDict, 'parentId')
  parent: RelationshipDict;

  @HasMany(() => RelationshipDict, 'parentId')
  children: RelationshipDict[];
}
