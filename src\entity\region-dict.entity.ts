import {
  Table,
  Column,
  DataType,
  HasMany,
  BelongsTo,
  ForeignKey,
  Model,
} from 'sequelize-typescript';
import { Mountain } from './mountain.entity';
import { WaterSystem } from './water-system.entity';
import { HistoricalElement } from './historical-element.entity';

export interface RegionDictAttributes {
  /** ID */
  id: number;
  /** 区域编码 */
  regionCode?: string;
  /** 区域名称 */
  regionName?: string;
  /** 父级区域ID */
  parentId?: number;
  /** 状态 */
  status?: number;
  /** 排序号 */
  sort?: number;
  /** 区域描述 */
  regionDesc?: string;
}

/**
 * 区域字典表模型
 */
@Table({
  tableName: 'region_dict',
  comment: '区域字典表',
})
export class RegionDict
  extends Model<RegionDictAttributes>
  implements RegionDictAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: true,
    comment: '区域编码（唯一）',
    field: 'region_code',
  })
  regionCode: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '区域名称',
    field: 'region_name',
  })
  regionName: string;

  @ForeignKey(() => RegionDict)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
    comment: '父级区域ID',
    field: 'parent_id',
  })
  parentId: number;

  @Column({
    type: DataType.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '状态（1启用，0禁用）',
  })
  status: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '排序号',
  })
  sort: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '区域描述',
    field: 'region_desc',
  })
  regionDesc: string;

  // 自关联关系
  @BelongsTo(() => RegionDict, 'parentId')
  parent: RegionDict;

  @HasMany(() => RegionDict, 'parentId')
  children: RegionDict[];

  // 关联关系
  @HasMany(() => Mountain, 'regionDictId')
  mountains: Mountain[];

  @HasMany(() => WaterSystem, 'regionDictId')
  waterSystems: WaterSystem[];

  @HasMany(() => HistoricalElement, 'regionDictId')
  historicalElements: HistoricalElement[];
}
