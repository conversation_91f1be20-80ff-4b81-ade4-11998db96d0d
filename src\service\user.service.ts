import { Provide, Inject } from '@midwayjs/core';
import { JwtService } from '@midwayjs/jwt';
import { CreateUserDTO, UpdateUserDTO, LoginDTO, LoginResponseDTO, UserResponseDTO } from '../dto/user.dto';
import { PageQueryDTO, PageResponseDTO } from '../dto/common.dto';

@Provide()
export class UserService {

  @Inject()
  jwtService: JwtService;

  /**
   * 用户登录
   */
  async login(loginDto: LoginDTO): Promise<LoginResponseDTO> {
    const { username, password } = loginDto;

    // 临时实现：只允许默认管理员登录
    if (username === 'admin' && password === 'admin123') {
      const token = await this.jwtService.sign({
        userId: 1,
        username: 'admin',
        role: 'admin'
      });

      const user = {
        id: 1,
        username: 'admin',
        role: 'admin',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return new LoginResponseDTO(token, user);
    }

    throw new Error('用户名或密码错误');
  }

  /**
   * 创建用户
   */
  async createUser(createUserDto: CreateUserDTO): Promise<UserResponseDTO> {
    // 临时实现，待Sequelize配置完成后实现
    const user = {
      id: 2,
      username: createUserDto.username,
      role: createUserDto.role || 'user',
      enabled: createUserDto.enabled !== false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    return new UserResponseDTO(user);
  }

  /**
   * 更新用户
   */
  async updateUser(id: number, updateUserDto: UpdateUserDTO): Promise<UserResponseDTO> {
    // 临时实现，待Sequelize配置完成后实现
    const user = {
      id,
      username: updateUserDto.username || 'admin',
      role: updateUserDto.role || 'admin',
      enabled: updateUserDto.enabled !== false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    return new UserResponseDTO(user);
  }

  /**
   * 删除用户
   */
  async deleteUser(id: number): Promise<void> {
    // 临时实现，待Sequelize配置完成后实现
    return;
  }

  /**
   * 获取用户详情
   */
  async getUserById(id: number): Promise<UserResponseDTO> {
    // 临时实现，待Sequelize配置完成后实现
    const user = {
      id,
      username: 'admin',
      role: 'admin',
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    return new UserResponseDTO(user);
  }

  /**
   * 分页获取用户列表
   */
  async getUserList(pageQuery: PageQueryDTO): Promise<PageResponseDTO<UserResponseDTO>> {
    // 临时实现，待Sequelize配置完成后实现
    const { page, pageSize } = pageQuery;
    const user = {
      id: 1,
      username: 'admin',
      role: 'admin',
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    const userDtos = [new UserResponseDTO(user)];
    return new PageResponseDTO(userDtos, 1, page, pageSize);
  }

  /**
   * 验证用户权限
   */
  async validateUser(userId: number): Promise<any> {
    // 临时实现，待Sequelize配置完成后实现
    if (userId === 1) {
      return {
        id: 1,
        username: 'admin',
        role: 'admin',
        enabled: true
      };
    }
    throw new Error('用户不存在或已被禁用');
  }
}
